{"execution_id": "exec_ae012109_1751149397", "symbol": "SOL", "timestamp": 1751149397, "datetime": "2025-06-29 07:23:17", "type": "execution_result", "data": {"symbol": "SOL", "strategy_id": "096a568c-6311-413a-8113-ddc67a1111da", "timestamp": 1751149397, "market_data": {"id": "market_SOL_1751149367", "symbol": "SOL", "timestamp": 1751149367, "datetime": "2025-06-29 07:22:47", "date": "2025-06-29", "time": "07:22:47", "price": 150.79, "open": 0.0, "high": 0.0, "low": 0.0, "close": 150.78, "volume": 17857371.05, "volume_24h": 17857371.05, "high_24h": 152.68, "low_24h": 141.07, "percent_change_24h": 5.662, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 28915501, "social_dominance": 0.006, "social_contributors": 289155, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1751149367, "news_count": 24, "ema_7": 150.79999999999998, "ema_14": 150.76, "ema_25": 150.60119999999998, "ema_50": 150.4874, "ema_99": 150.71949494949496, "ema_200": 149.0395, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.1183333333333327, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 80636767727.83, "recent_news_titles": ["SUI Recovers as Nasdaq-Listed Lion Group Says It Wants to Buy SUI, HYPE, and SOL", "<PERSON> Makes Rare Appearance with Wife and &#39;Respected&#39; Middle Child", "SOL Strategies Reveals Solana Strategic Ecosystem Reserve Initiative – Crypto News Bitcoin News", "Solana Gains DEX Marketshare But SOL Price Fails To Follow", "SUI Rebounds From Key Support as Nasdaq-Listed Lion Group Eyes Treasury Purchase"], "top_social_posts": [{"text": "CRYPTO LIVE TRADING | BTC, ETH, SOL | 28 june #btc #crypto #livetrading", "sentiment": 3.2, "platform": "youtube-video"}, {"text": "SOL RIPPING\nETH WILL FOLLOW\nBITCOIN $150,000 NEXT\n\nEverything according to plan…\n\njust sent out weekly TA report to all Pro members outlining next steps 📩 🚀", "sentiment": 3, "platform": "tweet"}, {"text": "🚨Solana Price Update: $SOL hits $151.07!  \n\nWE'RE BACK!?", "sentiment": 3.14, "platform": "tweet"}, {"text": "The market is pricing in a $SOL ETF approval with staking this week...", "sentiment": 3.21, "platform": "tweet"}, {"text": "Is this not crazy?\n\nWhy are SOL and ETH so low\n\nBitcoin is at $106,000.", "sentiment": 3.2, "platform": "tweet"}, {"text": "For some reason we need 420 $SOL wallets...\n\n🐽 👇", "sentiment": 3, "platform": "tweet"}, {"text": "$SOL is pumping hard! \n\nTime for Memecoins?", "sentiment": 2.85, "platform": "tweet"}, {"text": "there's at least 4 colossal bull catalysts for SOL coming up even before breakpoint\n\nand breakpoint always onboards a ton of new teams and institutions \n\nbig rest of the year coming up", "sentiment": 3.16, "platform": "tweet"}, {"text": "Just went through @wardenprotocol latest roadmap and it’s not just a vision, it’s execution in motion.\n\nWarden isn’t building another wallet. They’re building the interface layer for the next era of onchain interaction. Let me break it down\n\n→ Type-to-execute UX\nForget endless clicks. Just type “buy SOL,” “stake ETH,” or “track $TIA” and it’s done. <PERSON><PERSON> becomes command center. Web3 starts acting like Web2.\n\n→ Agent Hub = power tools for users\nWant auto-DCA, real-time analytics, or custom bots? Warden’s Agent Hub lets you deploy strategies and plug into sources like Kaito or Messari natively.\n\n→ Onboarding that doesn’t suck\nFiat ramps (via Coinbase), Solana EVM bridging, no app-switching. You open Warden, and it just… works. Finally.\n\n→ Support across ecosystems\nSolana, EVM, Cosmos one app to manage them all. Stake, LP, swap, track without jumping chains or juggling wallets.\n\n→ Gamified mechanics done right\nLeaderboards, token incentives, and coin-powered utility keep the loop fun and sticky. Engagement meets real yield.\n\n→ Gasless, voice-driven, AI-modular\nNo gas fees, talk-to-execute, and even BYOM (Bring Your Own Model). Custom AI flows + full privacy = big unlock for power users.\n\nNo VCs. No fluff. Just 600K users and a live product evolving fast.\n\nThis might just be the interface layer crypto was waiting for.\nTry it yourself →", "sentiment": 3.03, "platform": "tweet"}, {"text": "$SOL right there at resistance also", "sentiment": 2.89, "platform": "tweet"}], "recent_candles": [[1751148180000, "150.8400", "151.0300", "150.8200", "150.8200", "7266.49", 1751148239999, "1096738.410700", 1386, "3594.14", "542435.262700", "0"], [1751148240000, "150.8200", "150.9500", "150.8000", "150.9200", "5456.72", 1751148299999, "823386.962400", 979, "1959.08", "295598.075800", "0"], [1751148300000, "150.9200", "150.9400", "150.7300", "150.7700", "7796.47", 1751148359999, "1175893.809800", 1050, "2366.99", "356968.637500", "0"], [1751148360000, "150.7700", "150.8900", "150.7000", "150.8900", "6134.86", 1751148419999, "924795.232600", 961, "1879.34", "283322.711200", "0"], [1751148420000, "150.8900", "150.9500", "150.8000", "150.8700", "6990.23", 1751148479999, "1054693.300900", 1031, "3488.24", "526278.536700", "0"], [1751148480000, "150.8700", "150.9900", "150.8500", "150.9100", "7515.50", 1751148539999, "1134361.907800", 919, "2948.73", "445061.133400", "0"], [1751148540000, "150.9100", "150.9900", "150.9100", "150.9300", "3997.74", 1751148599999, "603425.090200", 667, "2316.71", "349691.512400", "0"], [1751148600000, "150.9300", "150.9600", "150.8000", "150.8100", "4666.81", 1751148659999, "704260.229900", 666, "1275.01", "192418.278200", "0"], [1751148660000, "150.8000", "150.8100", "150.7300", "150.7500", "6352.49", 1751148719999, "957762.387000", 951, "1863.72", "280996.962900", "0"], [1751148720000, "150.7400", "150.7900", "150.7400", "150.7700", "2655.44", 1751148779999, "400335.946100", 651, "1189.74", "179367.369400", "0"], [1751148780000, "150.7700", "150.7800", "150.7100", "150.7800", "5249.56", 1751148839999, "791332.047400", 811, "1963.89", "296060.908300", "0"], [1751148840000, "150.7800", "150.8200", "150.7100", "150.7200", "2214.98", 1751148899999, "333963.960000", 614, "958.27", "144486.457000", "0"], [1751148900000, "150.7200", "150.8000", "150.7100", "150.7300", "2379.64", 1751148959999, "358744.714400", 563, "1347.88", "203197.822800", "0"], [1751148960000, "150.7300", "150.8500", "150.7200", "150.8500", "4497.78", 1751149019999, "678262.271800", 600, "3816.54", "575514.270100", "0"], [1751149020000, "150.8500", "150.8900", "150.8400", "150.8900", "2251.25", 1751149079999, "339636.168100", 541, "1262.60", "190484.334200", "0"], [1751149080000, "150.9000", "150.9300", "150.7700", "150.8000", "5026.08", 1751149139999, "758055.740300", 779, "1832.85", "276412.531600", "0"], [1751149140000, "150.8000", "150.8300", "150.7300", "150.7500", "3245.07", 1751149199999, "489278.237400", 563, "1626.63", "245243.478100", "0"], [1751149200000, "150.7500", "150.8100", "150.7200", "150.7800", "4054.10", 1751149259999, "611153.523500", 529, "1288.14", "194194.514600", "0"], [1751149260000, "150.7700", "150.8700", "150.7500", "150.7600", "3382.34", 1751149319999, "510101.708800", 661, "1595.97", "240694.735800", "0"], [1751149320000, "150.7500", "150.8100", "150.7400", "150.7900", "2617.30", 1751149379999, "394622.466200", 442, "1399.37", "210983.563700", "0"]], "candles_count": 20, "data_timestamp": 1751149367, "has_timeseries_data": true, "short_term_change_pct": -0.006631299734760827, "recent_high": 150.93, "recent_low": 150.72}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "neutral", "reasoning": "The short-term 5-minute candle data shows a neutral trend with mixed price movements and weak volume, indicating no strong directional bias. No clear technical patterns or breakout signals are present. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "hold", "trading_direction": "neutral", "signal_direction": "neutral", "keywords": ["neutral trend", "weak volume", "no clear pattern"], "raw_response": " NO MARKDOWN.\n\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"The short-term 5-minute candle data shows a neutral trend with mixed price movements a...", "confidence": 0.35, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_7e62a954_1751149384"}, "strategy": {"strategy_id": "096a568c-6311-413a-8113-ddc67a1111da", "symbol": "SOL", "timestamp": 1751149397, "type": "none", "entry_price": 150.79, "stop_loss": 147.28, "take_profit": 144.23, "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 명확한 과열 또는 과냉 상태가 아니다. MACD는 0선 근처에서 횡보하며 단기적인 방향성이 불확실하다. 볼린저 밴드는 가격이 중간 영역에 위치해 있어 단기 변동성은 낮은 편이다. InCA의 HOLD 신호와 일치하며, 현재 시장 상황에서는 명확한 매수 또는 매도 신호가 나타나지 않아 무역을 권장하지 않는다.", "confidence": 0.75, "reasoning_card_id": "card_7e62a954_1751149384", "risk_level": "medium", "key_points": ["RSI는 중립 영역에 있으며 과열 또는 과냉 상태가 아님", "MACD는 0선 근처에서 횡보하며 방향성 불확실", "볼린저 밴드는 중간 영역에 있어 단기 변동성 낮음"], "market_context": {"price": 150.79, "percent_change_24h": 5.662, "timestamp": 1751149367}, "paper_based": false, "risk_reward": 0.0, "importance": 9.763123058703087, "consensus_signal": "none", "consensus_confidence": 0.75, "consensus_breakdown": {"short_term": {"action": "hold", "situation": "neutral", "importance": 0.5, "confidence": 0.35, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "none", "importance": 0.5, "confidence": 0.75, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "none", "consensus_confidence": 0.75, "should_execute": false, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - SOL"}, "execution_id": "exec_ae012109_1751149397"}}