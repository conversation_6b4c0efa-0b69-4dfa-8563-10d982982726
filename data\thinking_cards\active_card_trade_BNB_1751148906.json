{"card_id": "card_trade_BNB_1751148906_1751148907", "position_id": "trade_BNB_1751148906", "symbol": "BNB", "created_time": 1751148907.7840717, "last_updated": 1751148955.838805, "is_active": true, "position_info": {"symbol": "BNB", "direction": "short", "entry_price": 648.5, "quantity": 0.03, "value_usd": 22.2940725455, "strategy": {"type": "short", "direction": "short", "entry_price": 648.5, "symbol": "BNB", "confidence": 0.8, "timestamp": 1751148907}, "market_data": {"id": "market_BNB_1751148887", "symbol": "BNB", "timestamp": 1751148887, "datetime": "2025-06-29 07:14:47", "date": "2025-06-29", "time": "07:14:47", "price": 648.5, "open": 0.0, "high": 0.0, "low": 0.0, "close": 648.51, "volume": 129442.56, "volume_24h": 129442.56, "high_24h": 648.88, "low_24h": 644.0, "percent_change_24h": 0.323, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 11813285, "social_dominance": 0.638, "social_contributors": 118132, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1751148887, "news_count": 13, "ema_7": 648.3199999999999, "ema_14": 648.0550000000001, "ema_25": 647.7872, "ema_50": 647.5927999999999, "ema_99": 647.0071717171717, "ema_200": 646.4514999999999, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.075384615384616, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 91415301942.94, "recent_news_titles": ["BNB Price Eyes an 8% Surge Amid DEX Dominance and Stablecoin Growth", "World Liberty Financial, Re7 Scale USD1 on BNB", "Nano Labs Sets Sights on BNB Treasury With $500 Million Convertible Notes Deal – Crypto News Bitcoin News", "Binance Founder Declares 0.1 Bitcoin the Future American Dream – Featured Bitcoin News", "OORT DataHub Becomes the First DeAI dApp Launched on Binance Wallet - TechBullion"], "top_social_posts": [{"text": "$BTC Getting ready to dump, losing POC and dVWAP at 107.2k \n\nBinance futures orderbook has large asks above limiting upside during low volume weekend.\n\nMy trades and orderflow breakdowns:", "sentiment": 2.82, "platform": "tweet"}, {"text": "un qué???", "sentiment": 3, "platform": "tiktok-video"}, {"text": "this coin has:\n\n> $600m ath\n> binance perp listing\n> bybit spot listing\n> famous derivatives at $20m\n> ct still talking about it everyday\n> normies still using it as a meme\n\nand is actually breaking out at $50m\n\nwhat is it?", "sentiment": 3.24, "platform": "tweet"}, {"text": "$BTC\n\nStacked sell walls at 109-112 on Binance, Coinbase, Bitfinex. We will test this upper price region at some point. \n\nHowever, the million dollar question is if 1.) We take out late longs down to 105 or 2.) we push up first. 🎲\n\nTrading community -", "sentiment": 2.92, "platform": "tweet"}, {"text": "What <PERSON>tensor did for decentralised AI models...\n\nSahara is doing it for the entire AI stack.\n\nTwo very different visions are defining the next frontier in AI x crypto.\n\nLet’s break it down. 👇\n\n➢ Quick Stats\n\n⇝ Bittensor ($TAO)\n▸ $6.78B FDV\n▸ Listed on Coinbase\n\n⇝ Sahara\n▸ $850M FDV\n▸ Listed on Binance\n-----------------------------------\n➢ Philosophy\n\n⇝ Bittensor rewards ML models in a closed-loop competition.\n\n⇝ Sahara goes bigger: rewards all roles—data, compute, models, and agents.\nThink AI ecosystem, not just AI model.\n-----------------------------------\n➢ Architecture\n\n⇝ Bittensor = single subnet; all models compete there.\n\n⇝ Sahara = 4-layer stack:\n▸ Application layer\n▸ Transaction layer\n▸ Data layer\n▸ Execution layer\n\nBuilt for devs. Built to scale.\n-----------------------------------\n ➢ Use Cases\n\n⇝ Bittensor is great for raw model training.\n\n⇝ Sahara is built for real-world deployment.\nImagine launching AI agents like apps—traceable, monetizable, and composable.\n-----------------------------------\n ➢ Value Capture\n\n⇝ Bittensor rewards top models.\n\n⇝ Sahara shares value across the stack:\n▸ Data contributors\n▸ Model developers\n▸ Compute providers\n▸ App usage\n\nAn AI \"app store\" economy in the making.\n-----------------------------------\n➢ Bottom Line\n\nBittensor walked so @SaharaLabsAI could build.\n\nIf $TAO was v1 of decentralised AI—single-layer, model-first...\n\nThen Sahara is v2—modular, flexible, dev-friendly, and fully stacked.\n-----------------------------------\n➢ Now let's look at $Sahara Token.\n\nStrong token utility includes:\n▸ Access datasets, models, and compute resources\n▸ Pay per-inference and gas fees.\n▸ Stake to secure the network.\n▸ Vote on governance decisions\n\nSo the flywheel for $Sahara looks like this:\nMore data → More models → More AI tasks →\nToken utility keeps growing → increasing demand for the token.\n\nLet me know your thoughts about Sahara in the comments!", "sentiment": 3.1, "platform": "tweet"}, {"text": "<PERSON>'s Cat / $CAT is back around the levels where i opened my long ~2 months ago\n\nthe chart looks clearly bottomed and has been consolidating around these levels for ~4 months now (i.e. sellers are exhausted)\n\nit's also down 91% from ATHs\n\nmeanwhile, its cat coin peers that it held a similar range with during the last cat szn (i.e. $MEW, $POPCAT, $MOG, etc.) are sitting in the $240M–$360M market cap range, while CAT is only at $45M\n\nthe R/R for CAT here favors an aggressive move to the upside, especially if we get another cat season (we will!)\n\nit’s also listed on Binance, Bybit, OKX, Revolut, etc., making it easily accessible to hundreds of millions of retail users\n\nif I didn’t already have a position, this would be a trade I’d happily take — just like I did with my FARTCOIN and POPCAT plays at the bottom a few months ago", "sentiment": 3.02, "platform": "tweet"}, {"text": "$DMC sell pressure now seems to be in our past from the initial Binance Alpha 🙌\n\nNow it's time to see what a 50-year-old iconic brand can do in crypto with real-world assets! 🔥\n\n0x4c981f3ff786cdb9e514da897ab8a953647dae2ace9679e8358eec1e3e8871ac::dmc::DMC\n\nIYKYN 😎", "sentiment": 3.13, "platform": "tweet"}, {"text": "Okay, firstly I don’t have money oo but I want to give $20 to 5 people.\n\nIt may not be much but I really appreciate everyone who engages with me, I also do my best to engage back🙏\n\nI will pick winners in 12hrs \nPlease drop your Binance ID or Usdt wallet (bep 20)", "sentiment": 2.62, "platform": "tweet"}, {"text": "Which #memecoin will 100x this weekend?\n\n#memecoin Banks $BANE #Binance", "sentiment": 3.2, "platform": "tweet"}, {"text": "@binance Straight to the charts to see how it’s doing", "sentiment": 3.27, "platform": "tweet"}], "recent_candles": [[*************, "647.860", "647.860", "647.830", "647.830", "18.09", *************, "11719.55510", 109, "5.51", "3569.66660", "0"], [*************, "647.840", "647.840", "647.820", "647.820", "22.44", *************, "14537.19640", 116, "6.25", "4048.96410", "0"], [*************, "647.830", "648.090", "647.830", "648.030", "166.40", *************, "107826.29160", 678, "126.28", "81827.35260", "0"], [*************, "648.040", "648.040", "648.030", "648.030", "32.22", *************, "20879.64330", 123, "11.67", "7562.62680", "0"], [*************, "648.040", "648.160", "648.030", "648.050", "123.00", *************, "79715.92770", 269, "44.07", "28561.09540", "0"], [*************, "648.050", "648.150", "648.000", "648.000", "162.45", *************, "105283.36220", 913, "80.62", "52250.25190", "0"], [1751148060000, "648.010", "648.010", "647.840", "647.880", "122.57", 1751148119999, "79412.78690", 326, "65.06", "42150.21190", "0"], [1751148120000, "647.880", "647.990", "647.810", "647.940", "403.57", 1751148179999, "261464.87650", 397, "282.46", "183001.99230", "0"], [1751148180000, "647.940", "648.180", "647.940", "648.130", "249.79", 1751148239999, "161893.14900", 302, "169.77", "110028.31490", "0"], [1751148240000, "648.140", "648.480", "648.130", "648.420", "701.30", 1751148299999, "454677.80400", 2634, "584.04", "378648.18290", "0"], [1751148300000, "648.420", "648.700", "648.370", "648.640", "742.96", 1751148359999, "481846.11390", 3806, "464.27", "301099.01460", "0"], [1751148360000, "648.650", "648.780", "648.650", "648.780", "197.95", 1751148419999, "128413.26910", 383, "177.79", "115334.84510", "0"], [1751148420000, "648.780", "648.850", "648.780", "648.800", "181.50", 1751148479999, "117760.80080", 321, "48.18", "31259.49030", "0"], [1751148480000, "648.810", "648.880", "648.730", "648.740", "249.91", 1751148539999, "162149.56200", 310, "68.83", "44659.51010", "0"], [1751148540000, "648.740", "648.740", "648.730", "648.740", "44.65", 1751148599999, "28965.89380", 92, "9.93", "6441.98820", "0"], [1751148600000, "648.740", "648.790", "648.660", "648.670", "176.01", 1751148659999, "114185.89700", 312, "49.67", "32223.23240", "0"], [1751148660000, "648.670", "648.670", "648.540", "648.540", "77.61", 1751148719999, "50337.50840", 271, "6.73", "4365.21470", "0"], [1751148720000, "648.550", "648.550", "648.520", "648.530", "102.52", 1751148779999, "66487.70300", 210, "14.81", "9604.87420", "0"], [1751148780000, "648.530", "648.530", "648.450", "648.450", "72.29", 1751148839999, "46879.70220", 222, "10.35", "6712.26030", "0"], [1751148840000, "648.460", "648.560", "648.240", "648.240", "210.06", 1751148899999, "136208.94930", 434, "99.73", "64668.44850", "0"]], "candles_count": 20, "data_timestamp": 1751148887, "has_timeseries_data": true, "short_term_change_pct": -0.06628948463778962, "recent_high": 648.79, "recent_low": 648.24}, "entry_timestamp": 1751148906, "mode": "real"}, "completion_status": {"trend_prediction": false, "sela_matching": false, "timeframe_learning": true, "position_management": true}, "trend_prediction": {"short_term": null, "medium_term": null, "long_term": null, "confidence": 0.0, "prediction_time": 1751148907.7840717, "reasoning": ""}, "sela_matching": {"sela_prediction": null, "position_prediction": null, "match_score": 0.0, "accuracy_history": [], "last_comparison_time": 1751148907.7840717}, "timeframe_learning": {"short_term_results": [{"profit": 0.004036947480830112, "hold_time_minutes": 0.8166666666666667, "current_price": 648.5, "timestamp": 1751148955, "status": "ongoing"}], "medium_term_results": [], "long_term_results": [], "learning_insights": {}, "performance_metrics": {"max_profit": 0.004036947480830112, "min_profit": 0.004036947480830112}}, "position_management": {"current_action": "monitor_position", "decision_reason": "포지션 모니터링 (PnL: 0.00%)", "management_history": [{"timestamp": 1751148907.7859788, "action": "create_position", "reason": "short 포지션 생성", "market_condition": {"price": 648.5, "timestamp": 1751148906, "strategy_type": "short"}, "decision_confidence": 0.8}, {"timestamp": 1751148955.838805, "action": "monitor_position", "reason": "포지션 모니터링 (PnL: 0.00%)", "market_condition": {"price": 648.5, "pnl_pct": 0.004036947480830112, "hold_time_minutes": 0.8166666666666667, "timestamp": 1751148955}, "decision_confidence": 0.5}], "risk_assessment": {}, "profit_target": null, "stop_loss": null}}