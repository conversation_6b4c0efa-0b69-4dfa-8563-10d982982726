{"execution_id": "exec_79967ce0_1751149564", "symbol": "SOL", "timestamp": 1751149564, "datetime": "2025-06-29 07:26:04", "type": "execution_result", "data": {"symbol": "SOL", "strategy_id": "c401b83a-bd8a-4239-97b0-562e7397ea79", "timestamp": 1751149564, "market_data": {"id": "market_SOL_1751149542", "symbol": "SOL", "timestamp": 1751149542, "datetime": "2025-06-29 07:25:42", "date": "2025-06-29", "time": "07:25:42", "price": 150.83, "open": 0.0, "high": 0.0, "low": 0.0, "close": 150.82, "volume": 17856255.5, "volume_24h": 17856255.5, "high_24h": 152.68, "low_24h": 141.07, "percent_change_24h": 5.564, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 28914977, "social_dominance": 0.009, "social_contributors": 289149, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1751149542, "news_count": 24, "ema_7": 150.81857142857143, "ema_14": 150.7914285714286, "ema_25": 150.628, "ema_50": 150.49239999999998, "ema_99": 150.7330303030303, "ema_200": 149.06040000000002, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.1183333333333327, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 80659781191.98, "recent_news_titles": ["SUI Recovers as Nasdaq-Listed Lion Group Says It Wants to Buy SUI, HYPE, and SOL", "<PERSON> Makes Rare Appearance with Wife and &#39;Respected&#39; Middle Child", "SOL Strategies Reveals Solana Strategic Ecosystem Reserve Initiative – Crypto News Bitcoin News", "Solana Gains DEX Marketshare But SOL Price Fails To Follow", "SUI Rebounds From Key Support as Nasdaq-Listed Lion Group Eyes Treasury Purchase"], "top_social_posts": [{"text": "CRYPTO LIVE TRADING | BTC, ETH, SOL | 28 june #btc #crypto #livetrading", "sentiment": 3.2, "platform": "youtube-video"}, {"text": "SOL RIPPING\nETH WILL FOLLOW\nBITCOIN $150,000 NEXT\n\nEverything according to plan…\n\njust sent out weekly TA report to all Pro members outlining next steps 📩 🚀", "sentiment": 3, "platform": "tweet"}, {"text": "🚨Solana Price Update: $SOL hits $151.07!  \n\nWE'RE BACK!?", "sentiment": 3.14, "platform": "tweet"}, {"text": "The market is pricing in a $SOL ETF approval with staking this week...", "sentiment": 3.21, "platform": "tweet"}, {"text": "Is this not crazy?\n\nWhy are SOL and ETH so low\n\nBitcoin is at $106,000.", "sentiment": 3.2, "platform": "tweet"}, {"text": "For some reason we need 420 $SOL wallets...\n\n🐽 👇", "sentiment": 3, "platform": "tweet"}, {"text": "$SOL is pumping hard! \n\nTime for Memecoins?", "sentiment": 2.85, "platform": "tweet"}, {"text": "there's at least 4 colossal bull catalysts for SOL coming up even before breakpoint\n\nand breakpoint always onboards a ton of new teams and institutions \n\nbig rest of the year coming up", "sentiment": 3.16, "platform": "tweet"}, {"text": "Just went through @wardenprotocol latest roadmap and it’s not just a vision, it’s execution in motion.\n\nWarden isn’t building another wallet. They’re building the interface layer for the next era of onchain interaction. Let me break it down\n\n→ Type-to-execute UX\nForget endless clicks. Just type “buy SOL,” “stake ETH,” or “track $TIA” and it’s done. <PERSON><PERSON> becomes command center. Web3 starts acting like Web2.\n\n→ Agent Hub = power tools for users\nWant auto-DCA, real-time analytics, or custom bots? Warden’s Agent Hub lets you deploy strategies and plug into sources like Kaito or Messari natively.\n\n→ Onboarding that doesn’t suck\nFiat ramps (via Coinbase), Solana EVM bridging, no app-switching. You open Warden, and it just… works. Finally.\n\n→ Support across ecosystems\nSolana, EVM, Cosmos one app to manage them all. Stake, LP, swap, track without jumping chains or juggling wallets.\n\n→ Gamified mechanics done right\nLeaderboards, token incentives, and coin-powered utility keep the loop fun and sticky. Engagement meets real yield.\n\n→ Gasless, voice-driven, AI-modular\nNo gas fees, talk-to-execute, and even BYOM (Bring Your Own Model). Custom AI flows + full privacy = big unlock for power users.\n\nNo VCs. No fluff. Just 600K users and a live product evolving fast.\n\nThis might just be the interface layer crypto was waiting for.\nTry it yourself →", "sentiment": 3.03, "platform": "tweet"}, {"text": "$SOL right there at resistance also", "sentiment": 2.89, "platform": "tweet"}], "recent_candles": [[1751148360000, "150.7700", "150.8900", "150.7000", "150.8900", "6134.86", 1751148419999, "924795.232600", 961, "1879.34", "283322.711200", "0"], [1751148420000, "150.8900", "150.9500", "150.8000", "150.8700", "6990.23", 1751148479999, "1054693.300900", 1031, "3488.24", "526278.536700", "0"], [1751148480000, "150.8700", "150.9900", "150.8500", "150.9100", "7515.50", 1751148539999, "1134361.907800", 919, "2948.73", "445061.133400", "0"], [1751148540000, "150.9100", "150.9900", "150.9100", "150.9300", "3997.74", 1751148599999, "603425.090200", 667, "2316.71", "349691.512400", "0"], [1751148600000, "150.9300", "150.9600", "150.8000", "150.8100", "4666.81", 1751148659999, "704260.229900", 666, "1275.01", "192418.278200", "0"], [1751148660000, "150.8000", "150.8100", "150.7300", "150.7500", "6352.49", 1751148719999, "957762.387000", 951, "1863.72", "280996.962900", "0"], [1751148720000, "150.7400", "150.7900", "150.7400", "150.7700", "2655.44", 1751148779999, "400335.946100", 651, "1189.74", "179367.369400", "0"], [1751148780000, "150.7700", "150.7800", "150.7100", "150.7800", "5249.56", 1751148839999, "791332.047400", 811, "1963.89", "296060.908300", "0"], [1751148840000, "150.7800", "150.8200", "150.7100", "150.7200", "2214.98", 1751148899999, "333963.960000", 614, "958.27", "144486.457000", "0"], [1751148900000, "150.7200", "150.8000", "150.7100", "150.7300", "2379.64", 1751148959999, "358744.714400", 563, "1347.88", "203197.822800", "0"], [1751148960000, "150.7300", "150.8500", "150.7200", "150.8500", "4497.78", 1751149019999, "678262.271800", 600, "3816.54", "575514.270100", "0"], [1751149020000, "150.8500", "150.8900", "150.8400", "150.8900", "2251.25", 1751149079999, "339636.168100", 541, "1262.60", "190484.334200", "0"], [1751149080000, "150.9000", "150.9300", "150.7700", "150.8000", "5026.08", 1751149139999, "758055.740300", 779, "1832.85", "276412.531600", "0"], [1751149140000, "150.8000", "150.8300", "150.7300", "150.7500", "3245.07", 1751149199999, "489278.237400", 563, "1626.63", "245243.478100", "0"], [1751149200000, "150.7500", "150.8100", "150.7200", "150.7800", "4054.10", 1751149259999, "611153.523500", 529, "1288.14", "194194.514600", "0"], [1751149260000, "150.7700", "150.8700", "150.7500", "150.7600", "3382.34", 1751149319999, "510101.708800", 661, "1595.97", "240694.735800", "0"], [1751149320000, "150.7500", "150.8100", "150.7400", "150.7900", "2906.49", 1751149379999, "438226.556100", 514, "1619.78", "244217.027000", "0"], [1751149380000, "150.7900", "150.9400", "150.7800", "150.8600", "4587.56", 1751149439999, "692148.240500", 857, "3294.21", "497034.179000", "0"], [1751149440000, "150.8600", "150.9400", "150.8500", "150.8700", "4803.18", 1751149499999, "724777.924500", 671, "1693.44", "255548.616400", "0"], [1751149500000, "150.8800", "150.8800", "150.7700", "150.8300", "2292.50", 1751149559999, "345711.698200", 558, "1267.80", "191172.458000", "0"]], "candles_count": 20, "data_timestamp": 1751149542, "has_timeseries_data": true, "short_term_change_pct": 0.04643141416822871, "recent_high": 150.94, "recent_low": 150.74}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "neutral", "reasoning": "Short-term candle data shows a neutral trend with mixed movement, but recent 1-minute candles indicate slight bullish momentum with higher volume in the third and fourth candles. No strong patterns or breakouts are evident, leading to a neutral overall assessment. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "hold", "trading_direction": "neutral", "signal_direction": "neutral", "keywords": ["neutral trend", "mixed movement", "volume confirmation"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term candle data shows a neutral trend with mixed movement, but recent 1-minute c...", "confidence": 0.35, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_72733b83_1751149552"}, "strategy": {"strategy_id": "c401b83a-bd8a-4239-97b0-562e7397ea79", "symbol": "SOL", "timestamp": 1751149564, "type": "none", "entry_price": 150.83, "stop_loss": 147.32, "take_profit": 144.29, "reasoning": "현재 시장 데이터 분석 결과, 가격은 중립적인 영역에 위치하고 있으며, RSI(14)는 50을 기준으로 상하 진동 중. 볼린저 밴드는 가격이 중앙선 근처에 머물고 있어 단기적인 방향성은 불확실. MACD는 0을 중심으로 횡보 중이며, 과도한 매수/매도 신호는 관찰되지 않음. InCA의 HOLD 신호와 일치하며, 현재 시장 상황에서는 보류가 적절함.", "confidence": 0.75, "reasoning_card_id": "card_72733b83_1751149552", "risk_level": "medium", "key_points": ["RSI(14)가 50 근처에서 진동", "볼린저 밴드 중앙선 근처 이동", "MACD가 0을 중심으로 횡보"], "market_context": {"price": 150.83, "percent_change_24h": 5.564, "timestamp": 1751149542}, "paper_based": false, "risk_reward": 0.0, "importance": 9.774615274633765, "consensus_signal": "none", "consensus_confidence": 0.75, "consensus_breakdown": {"short_term": {"action": "hold", "situation": "neutral", "importance": 0.5, "confidence": 0.35, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "none", "importance": 0.5, "confidence": 0.75, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "none", "consensus_confidence": 0.75, "should_execute": false, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - SOL"}, "execution_id": "exec_79967ce0_1751149564"}}